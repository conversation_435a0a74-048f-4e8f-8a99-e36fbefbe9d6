
import React, { useRef } from 'react';
import { ExternalLink } from 'lucide-react';
import { gsap } from 'gsap';

interface ProjectProps {
  project: {
    id: number;
    title: string;
    category: string;
    technologies: string;
    imageUrl: string;
    description: string;
  };
}

const ProjectCard = ({ project }: ProjectProps) => {
  const cardRef = useRef<HTMLDivElement>(null);
  
  const handleHover = () => {
    gsap.to(cardRef.current?.querySelector('.project-image'), {
      scale: 1.05,
      duration: 0.4,
    });
    
    gsap.to(cardRef.current?.querySelector('.view-project'), {
      opacity: 1,
      y: 0,
      duration: 0.3,
    });
  };
  
  const handleHoverExit = () => {
    gsap.to(cardRef.current?.querySelector('.project-image'), {
      scale: 1,
      duration: 0.4,
    });
    
    gsap.to(cardRef.current?.querySelector('.view-project'), {
      opacity: 0,
      y: 10,
      duration: 0.3,
    });
  };

  return (
    <div 
      ref={cardRef} 
      className="project-card"
      onMouseEnter={handleHover}
      onMouseLeave={handleHoverExit}
    >
      <div className="relative overflow-hidden rounded-lg mb-5 group cursor-pointer">
        <div className="aspect-[16/9] bg-agency-darker relative overflow-hidden">
          <img 
            src={project.imageUrl} 
            alt={project.title}
            className="project-image w-full h-full object-cover transition-transform duration-500 ease-out"
            onError={(e) => {
              // Fallback for missing image
              const target = e.target as HTMLImageElement;
              target.src = "https://placehold.co/600x400/080809/39FF14?text=SquareUp+Project";
            }}
          />
          <div className="absolute inset-0 bg-agency-darker bg-opacity-60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="view-project opacity-0 translate-y-4">
              <button className="px-5 py-2.5 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all flex items-center gap-2">
                View Project <ExternalLink size={16} />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <h3 className="text-xl font-semibold text-white mb-2">{project.title}</h3>
      
      <div className="mb-3 text-sm">
        <span className="text-agency-white-muted">{project.category} • </span>
        <span className="text-agency-green">{project.technologies}</span>
      </div>
      
      <p className="text-agency-white-muted text-sm line-clamp-3">
        {project.description}
      </p>
    </div>
  );
};

export default ProjectCard;
