
import React, { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

const FaqItem = ({ 
  question, 
  answer,
  isOpen,
  onClick,
  index
}: { 
  question: string, 
  answer: string,
  isOpen: boolean,
  onClick: () => void,
  index: number
}) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    gsap.fromTo(
      itemRef.current,
      { y: 20, opacity: 0 },
      { 
        y: 0, 
        opacity: 1, 
        duration: 0.5,
        delay: index * 0.1,
        scrollTrigger: {
          trigger: itemRef.current,
          start: "top 85%",
        }
      }
    );
  }, [index]);

  useEffect(() => {
    if (isOpen) {
      gsap.to(contentRef.current, { 
        height: "auto", 
        duration: 0.3, 
        ease: "power2.out",
        opacity: 1
      });
    } else {
      gsap.to(contentRef.current, { 
        height: 0, 
        duration: 0.3, 
        ease: "power2.out",
        opacity: 0
      });
    }
  }, [isOpen]);

  return (
    <div 
      ref={itemRef}
      className="border-b border-white/10 last:border-b-0 py-5"
    >
      <button
        className="flex items-center justify-between w-full text-left"
        onClick={onClick}
        aria-expanded={isOpen}
      >
        <h3 className="text-lg font-medium text-white">{question}</h3>
        <ChevronDown
          className={cn(
            "w-5 h-5 text-agency-green transition-transform duration-300",
            isOpen ? "transform rotate-180" : ""
          )}
        />
      </button>
      <div 
        ref={contentRef}
        className="overflow-hidden h-0 opacity-0"
      >
        <p className="pt-4 pb-2 text-agency-white-muted">{answer}</p>
      </div>
    </div>
  );
};

const FaqSection = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);
  const sectionRef = useRef<HTMLDivElement>(null);
  const headingRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    gsap.fromTo(
      headingRef.current,
      { y: 30, opacity: 0 },
      { 
        y: 0, 
        opacity: 1, 
        duration: 0.6,
        scrollTrigger: {
          trigger: headingRef.current,
          start: "top 85%",
        }
      }
    );
    
    return () => {
      ScrollTrigger.getAll().forEach(t => t.kill());
    };
  }, []);

  const faqs = [
    {
      question: "What services does SquareUp offer?",
      answer: "We provide end-to-end digital product services including UX/UI design, web and mobile development, branding, product strategy, and ongoing maintenance and support."
    },
    {
      question: "How long does a typical project take?",
      answer: "Project timelines vary depending on scope and complexity. A typical MVP might take 2-3 months, while more comprehensive products could take 4-6 months or more. We'll provide detailed timelines during our initial consultation."
    },
    {
      question: "Do you work with startups?",
      answer: "Absolutely! We love working with startups and have special packages designed to meet the unique needs and constraints of early-stage companies."
    },
    {
      question: "What is your design process like?",
      answer: "Our design process includes discovery and research, wireframing, visual design, prototyping, and user testing. We take an iterative approach to ensure the final product meets both user needs and business objectives."
    },
    {
      question: "Do you offer ongoing support?",
      answer: "Yes, we provide ongoing support and maintenance packages to ensure your product continues to evolve and perform optimally after launch."
    },
    {
      question: "What technologies do you specialize in?",
      answer: "We specialize in modern web and mobile technologies including React, React Native, Node.js, Next.js, and various other frameworks and tools that enable us to build high-performance, scalable digital products."
    }
  ];

  const toggleFaq = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section
      ref={sectionRef}
      className="section-padding bg-agency-darker relative overflow-hidden text-white"
    >
      <div className="container mx-auto">
        <div ref={headingRef} className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            Frequently Asked Questions
          </h2>
          <p className="text-agency-white-muted max-w-2xl mx-auto">
            Everything you need to know about our services and process
          </p>
        </div>

        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <FaqItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              isOpen={openIndex === index}
              onClick={() => toggleFaq(index)}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  )
};

export default FaqSection;
