
import React, { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import ProcessHero from '@/components/ProcessHero';
import ProcessSteps from '@/components/ProcessSteps';
import ContactForm from '@/components/ContactForm';

gsap.registerPlugin(ScrollTrigger);

const Process = () => {
  useEffect(() => {
    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden">
      <Navbar />
      <ProcessHero />
      <ProcessSteps />
      <div className="bg-agency-dark py-10 text-center border-t border-agency-white-muted/10">
        <div className="container mx-auto px-4">
          <p className="text-agency-white-muted mb-6">Thank you for your interest in SquareUp.</p>
          <a href="#contact-form" className="inline-flex px-8 py-3 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow">
            Start Project
          </a>
        </div>
      </div>
      <ContactForm />
      <Footer />
    </div>
  );
};

export default Process;
