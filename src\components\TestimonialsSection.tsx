
import React, { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { cn } from '@/lib/utils';

gsap.registerPlugin(ScrollTrigger);

const TestimonialCard = ({ 
  quote, 
  name, 
  position, 
  company, 
  isActive 
}: { 
  quote: string, 
  name: string, 
  position: string, 
  company: string,
  isActive: boolean
}) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isActive) {
      gsap.fromTo(
        cardRef.current,
        { opacity: 0, scale: 0.95 },
        { opacity: 1, scale: 1, duration: 0.5, ease: "power2.out" }
      );
    }
  }, [isActive]);

  return (
    <div
      ref={cardRef}
      className={cn(
        "bg-agency-darker p-8 rounded-lg border border-white/10 transition-opacity duration-300",
        isActive ? "opacity-100" : "opacity-0 absolute top-0 left-0 right-0"
      )}
    >
      <p className="text-lg md:text-xl mb-8 text-agency-white-muted">"{quote}"</p>
      <div className="flex items-center">
        <div className="w-12 h-12 bg-agency-green bg-opacity-20 rounded-full flex items-center justify-center">
          <span className="text-agency-green font-bold">{name.charAt(0)}</span>
        </div>
        <div className="ml-4">
          <h4 className="font-medium text-white">{name}</h4>
          <p className="text-sm text-agency-white-muted">{position}, {company}</p>
        </div>
      </div>
    </div>
  );
};

const TestimonialsSection = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const headingRef = useRef<HTMLDivElement>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    gsap.fromTo(
      headingRef.current,
      { y: 30, opacity: 0 },
      { 
        y: 0, 
        opacity: 1, 
        duration: 0.6,
        scrollTrigger: {
          trigger: headingRef.current,
          start: "top 85%",
        }
      }
    );

    // Auto-scroll testimonials
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);

    return () => {
      ScrollTrigger.getAll().forEach(t => t.kill());
      clearInterval(interval);
    };
  }, []);

  const testimonials = [
    {
      quote: "Working with SquareUp transformed our digital presence. Their team delivered a product that exceeded our expectations and significantly improved our user engagement.",
      name: "Sarah Johnson",
      position: "Product Director",
      company: "TechCorp"
    },
    {
      quote: "The team at SquareUp approached our project with incredible attention to detail and creativity. Their engineering expertise made our complex requirements seem simple.",
      name: "Michael Chang",
      position: "CTO",
      company: "Innovate Inc"
    },
    {
      quote: "We've worked with many design agencies before, but SquareUp's end-to-end approach and commitment to our success was unmatched. They're truly partners, not just vendors.",
      name: "Emma Rodriguez",
      position: "Marketing VP",
      company: "GrowFast"
    },
    {
      quote: "Our app redesign project was delivered on time and on budget, but what impressed us most was how the team anticipated problems before they occurred and handled them proactively.",
      name: "David Wilson",
      position: "CEO",
      company: "NextLevel"
    }
  ];

  return (
    <section
      ref={sectionRef}
      className="section-padding bg-agency-dark relative overflow-hidden"
    >
      <div className="container mx-auto">
        <div ref={headingRef} className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            What our Clients say About us
          </h2>
          <p className="text-agency-white-muted max-w-2xl mx-auto">
            Don't just take our word for it - hear from some of our amazing
            clients
          </p>
        </div>

        <div className="max-w-3xl mx-auto relative min-h-[300px]">
          {testimonials.map((testimonial, index) => (
            <TestimonialCard
              key={index}
              quote={testimonial.quote}
              name={testimonial.name}
              position={testimonial.position}
              company={testimonial.company}
              isActive={index === activeIndex}
            />
          ))}

          <div className="flex justify-center mt-8 gap-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={cn(
                  'w-3 h-3 rounded-full transition-colors duration-300',
                  index === activeIndex ? 'bg-agency-green' : 'bg-white/20'
                )}
                onClick={() => setActiveIndex(index)}
                aria-label={`Testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
};

export default TestimonialsSection;
