
import React, { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import WorkHero from '@/components/WorkHero';
import ProjectGrid from '@/components/ProjectGrid';

gsap.registerPlugin(ScrollTrigger);

const Work = () => {
  useEffect(() => {
    // Clean up on component unmount
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <div className="bg-agency-dark min-h-screen overflow-x-hidden">
      <Navbar />
      <WorkHero />
      <ProjectGrid />
      <div className="bg-agency-darker py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-8">Let us Bring your Ideas to Life in the Digital World.</h2>
          <p className="text-agency-white-muted max-w-3xl mx-auto mb-10">
            From concept to creation, we'll help you build solutions that deliver real value. 
            Our experienced team specializes in crafting beautiful and performant applications for businesses of all sizes.
          </p>
          <a
            href="/#contact"
            className="inline-flex px-8 py-3 bg-agency-green text-agency-dark font-medium rounded-md hover:bg-opacity-90 transition-all btn-glow"
          >
            Get Started
          </a>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Work;
