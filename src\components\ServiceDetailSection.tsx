
import React, { useRef, useEffect } from 'react';
import gsap from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import * as LucideIcons from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

interface ServiceItem {
  title: string;
  icon: keyof typeof LucideIcons;
}

interface ServiceCategory {
  category: string;
  items: ServiceItem[];
}

interface ServiceDetailSectionProps {
  title: string;
  description: string;
  services: ServiceCategory[];
}

const ServiceCard = ({ title, icon }: ServiceItem) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    gsap.fromTo(
      cardRef.current,
      { y: 20, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.5,
        scrollTrigger: {
          trigger: cardRef.current,
          start: "top 85%",
        }
      }
    );
  }, []);

  // Fix for the Icon component usage - need to use dynamic access with type assertions
  const IconComponent = LucideIcons[icon] as React.ElementType;

  return (
    <div ref={cardRef} className="p-6 bg-agency-darker border border-white/10 rounded-md flex flex-col items-center text-center card-hover">
      <div className="w-12 h-12 bg-agency-green bg-opacity-10 rounded-full flex items-center justify-center mb-4">
        <IconComponent className="w-6 h-6 text-agency-green" />
      </div>
      <h4 className="text-lg font-semibold text-white mb-2">{title}</h4>
    </div>
  );
};

const ServiceDetailSection = ({ title, description, services }: ServiceDetailSectionProps) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const descRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Animate title and description
    gsap.fromTo(
      titleRef.current,
      { y: 30, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.6,
        scrollTrigger: {
          trigger: titleRef.current,
          start: "top 85%",
        }
      }
    );

    gsap.fromTo(
      descRef.current,
      { y: 20, opacity: 0 },
      {
        y: 0,
        opacity: 1,
        duration: 0.6,
        delay: 0.2,
        scrollTrigger: {
          trigger: descRef.current,
          start: "top 85%",
        }
      }
    );
    
    return () => {
      ScrollTrigger.getAll().forEach(t => t.kill());
    };
  }, []);

  return (
    <section ref={sectionRef} className="py-20 border-b border-white/10">
      <div className="container mx-auto px-4">
        <div ref={titleRef} className="mb-4">
          <h2 className="text-3xl md:text-4xl font-bold text-white">{title}</h2>
        </div>
        <div ref={descRef} className="mb-12 max-w-4xl">
          <p className="text-agency-white-muted">{description}</p>
        </div>

        {services.map((serviceCategory, index) => (
          <div key={index} className="mb-16 last:mb-0">
            <h3 className="text-xl font-semibold mb-6 text-white">
              {serviceCategory.category}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {serviceCategory.items.map((item, itemIndex) => (
                <ServiceCard
                  key={itemIndex}
                  title={item.title}
                  icon={item.icon}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </section>
  )
};

export default ServiceDetailSection;
