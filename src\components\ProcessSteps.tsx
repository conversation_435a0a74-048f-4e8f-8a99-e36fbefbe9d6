
import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import ProcessStep from './ProcessStep';

gsap.registerPlugin(ScrollTrigger);

const ProcessSteps = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const introRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    gsap.fromTo(
      introRef.current,
      { opacity: 0, y: 30 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.8,
        scrollTrigger: {
          trigger: introRef.current,
          start: "top 80%",
        }
      }
    );
    
    return () => {
      ScrollTrigger.getAll().forEach(t => t.kill());
    };
  }, []);

  const processSteps = [
    {
      number: "01",
      title: "Discovery",
      description: "We begin by thoroughly understanding your business goals, target audience, and project requirements. We conduct in-depth interviews to gather insights and define project objectives, allowing us to develop a tailored strategy."
    },
    {
      number: "02",
      title: "Planning and Strategy",
      description: "Based on the gathered information, we create a comprehensive project plan and timeline. This includes scope definition, technology selection, resource allocation, and task prioritization. We collaborate closely with you to align our strategy with your vision."
    },
    {
      number: "03",
      title: "Design",
      description: "Our design phase focuses on creating compelling visuals and interfaces for your design. We create wireframes, mockups, and interactive prototypes to showcase the user journey, user experience, and overall design aesthetics. We iterate on designs based on your feedback to ensure they meet your expectations."
    },
    {
      number: "04",
      title: "Development",
      description: "Once the designs are approved, our skilled development team brings them to life. We use cutting-edge technologies and coding best practices to build robust and scalable digital products. Throughout the development phase, we maintain open communication to keep you informed about progress, challenges, or concerns."
    },
    {
      number: "05", 
      title: "Testing and Quality Assurance",
      description: "We conduct rigorous testing to ensure that your digital product functions flawlessly across all devices, browsers, and under various testing scenarios. Our experienced team meticulously checks for bugs, usability issues, and performance bottlenecks. We strive for a seamless user experience and a high level of quality."
    },
    {
      number: "06",
      title: "Deployment and Launch",
      description: "When your digital product is thoroughly tested and meets your satisfaction, we prepare for deployment. We handle all the technical aspects of deploying your product, ensuring a smooth transition from development to the live environment. We guide you through the hosting, configuration options, and manage the required infrastructure."
    },
    {
      number: "07",
      title: "Post-Launch Support",
      description: "Our commitment to your success doesn't end with the launch. We provide ongoing support and maintenance services to ensure your digital product continues to function optimally. We address any issues that may arise during your usage, including bug fixes, feature enhancements, security updates, and technical support."
    },
    {
      number: "08",
      title: "Continuous Improvement",
      description: "We believe in continuous improvement and strive to optimize your digital product over time based on user feedback, analytics, and market trends. We provide regular reports, recommend updates based on performance metrics, review requirements and updates to keep your digital product ahead of the curve."
    }
  ];

  return (
    <div ref={sectionRef} className="bg-agency-dark">
      <div className="container mx-auto px-4 py-16 md:py-24">
        <div ref={introRef} className="max-w-3xl mb-16">
          <h2 className="text-2xl md:text-3xl font-bold mb-6 text-white">
            At SquareUp
          </h2>
          <p className="text-agency-white-muted mb-6">
            We follow a structured and experiential process to ensure the
            necessary delivery of innovative digital products. Our process
            combines industry best practices, creative thinking, and a
            user-centric approach.
          </p>
          <p className="text-agency-white-muted">
            Here is an overview of our typical process:
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-16">
          {processSteps.map((step, index) => (
            <ProcessStep
              key={step.number}
              number={step.number}
              title={step.title}
              description={step.description}
              index={index}
            />
          ))}
        </div>
      </div>
    </div>
  )
};

export default ProcessSteps;
