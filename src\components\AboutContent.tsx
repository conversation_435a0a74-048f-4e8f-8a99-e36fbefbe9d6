
import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

const AboutContent = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: sectionRef.current,
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none none'
      }
    });
    
    tl.fromTo(
      textRef.current,
      { opacity: 0, x: -30 },
      { opacity: 1, x: 0, duration: 0.8, ease: "power2.out" }
    ).fromTo(
      imageRef.current,
      { opacity: 0, x: 30 },
      { opacity: 1, x: 0, duration: 0.8, ease: "power2.out" },
      "-=0.6"
    );
    
    return () => {
      if (tl.scrollTrigger) {
        tl.scrollTrigger.kill();
      }
      tl.kill();
    };
  }, []);

  return (
    <section ref={sectionRef} className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center">
          <div ref={textRef} className="order-2 md:order-1">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-white">
              About SquareUp
            </h2>
            <p className="text-agency-white-muted mb-4">
              SquareUp is a digital product agency that is passionate about
              crafting exceptional digital experiences. We specialize in design,
              engineering, and project management, helping businesses thrive in
              the digital landscape.
            </p>
            <p className="text-agency-white-muted mb-4">
              At SquareUp, we follow a structured and collaborative process to
              ensure the successful delivery of exceptional digital products.
              Our work is guided by industry best practices, creative thinking,
              and a client-centric approach.
            </p>
          </div>
          <div
            ref={imageRef}
            className="order-1 md:order-2 flex justify-center"
          >
            <div className="bg-gradient-to-r from-agency-dark to-agency-darker p-6 rounded-md border border-agency-green/20 relative">
              <div className="w-56 h-56 flex items-center justify-center">
                <div className="w-32 h-32 bg-agency-green/10 rounded-md relative p-4 border border-agency-green">
                  <div className="absolute w-full h-full top-0 left-0 bg-gradient-to-tr from-agency-green/10 to-transparent"></div>
                  <div className="w-full h-full bg-agency-darker border border-agency-green/40 flex items-center justify-center">
                    <div className="w-16 h-16 bg-agency-green rounded-md flex items-center justify-center">
                      <svg
                        className="w-10 h-10 text-agency-dark"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                      >
                        <path d="M21 16.5c0 .38-.21.71-.53.88l-7.9 4.44c-.16.12-.36.18-.57.18-.21 0-.41-.06-.57-.18l-7.9-4.44A.991.991 0 0 1 3 16.5v-9c0-.38.21-.71.53-.88l7.9-4.44c.16-.12.36-.18.57-.18.21 0 .41.06.57.18l7.9 4.44c.32.17.53.5.53.88v9zM12 4.15 5 8.09v7.82l7 3.94 7-3.94V8.09l-7-3.94z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
              <div className="absolute top-0 bottom-0 left-1/2 w-px bg-agency-green/30"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
};

export default AboutContent;
